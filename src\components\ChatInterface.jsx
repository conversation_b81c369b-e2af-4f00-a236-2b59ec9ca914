"use client";
import React, { useState, useEffect, useRef } from "react";
import { SUGGESTION_CARDS } from "../utils/constants";
import { getSSEUrl, API_ENDPOINTS } from "../utils/config";
import { FaArrowUp, FaChevronLeft, FaChevronRight } from "react-icons/fa6";
import axios from "axios";

const ChatInterface = ({ slug, query }) => {
  const [message, setMessage] = useState("");
  const [messages, setMessages] = useState([]);
  const [currentSlide, setCurrentSlide] = useState(0);
  const [touchStart, setTouchStart] = useState(0);
  const [touchEnd, setTouchEnd] = useState(0);
  const [isMobile, setIsMobile] = useState(false);
  const [isTyping, setIsTyping] = useState(false);
  const [showInitialUI, setShowInitialUI] = useState(false);
  const [isBotTyping, setIsBotTyping] = useState(false);
  const [isBotThinking, setIsBotThinking] = useState(false);
  const [sseConnection, setSseConnection] = useState(null);
  const messagesEndRef = useRef(null);
  const carouselRef = useRef(null);
  const inputRef = useRef(null);
  const desktopMessagesContainerRef = useRef(null);
  const mobileMessagesContainerRef = useRef(null);

  // Detect iOS for targeted fixes
  const isIOS = () => {
    if (typeof window !== "undefined") {
      return /iPhone|iPad|iPod/i.test(navigator.userAgent);
    }
    return false;
  };

  const username = slug;

  useEffect(() => {
    if (username && typeof window !== "undefined") {
      localStorage.setItem("customerName_userId", username);
      const existingUserId = localStorage.getItem("userID");

      if (existingUserId) {
        fetchExistingMessages(existingUserId);
      } else {
        initializeChatSession();
      }
    }
  }, [username]);

  const fetchExistingMessages = async (userId) => {
    try {
      const response = await axios.get(
        `${API_ENDPOINTS.CHAT_MESSAGES}?userId=${userId}`
      );

      if (response.data && response.data.length > 0) {
        setMessages(
          response.data.map((msg) => ({
            text: msg.message,
            timestamp: new Date(msg.createdAt).getTime(),
            type: msg.type,
            source: msg.source,
          }))
        );
      }

      connectToSSE(userId);
    } catch (error) {
      initializeChatSession();
    }
  };

  const initializeChatSession = async () => {
    try {
      const response = await axios.post(API_ENDPOINTS.CHAT_INIT, {
        customerName: username,
      });

      if (response.data && response.data.userId) {
        localStorage.setItem("userId", response.data.userId);
        localStorage.setItem("userID", response.data._id);
        fetchExistingMessages(response.data._id);
        connectToSSE(response.data.userId);
      }
    } catch (error) {
      // Handle initialization error silently
    }
  };

  const connectToSSE = (userId) => {
    try {
      if (sseConnection) {
        sseConnection.close();
      }

      const sseUrl = getSSEUrl(userId);
      const eventSource = new EventSource(sseUrl);
      setSseConnection(eventSource);

      eventSource.addEventListener("message", ({ data }) => {
        try {
          const contents = JSON.parse(data);
          handleSSEMessage(contents);
        } catch (error) {
          // Handle parsing error silently
        }
      });

      eventSource.addEventListener("error", () => {
        // Handle connection error silently
      });
    } catch (error) {
      // Handle SSE connection error silently
    }
  };

  const handleSSEMessage = (data) => {
    const subtype = data.subType || data.subtype || data.type || "UNKNOWN";
    const content = data.content || "";
    const message = data.message || data.text || "";

    if (["TYPING", "THINKING", "BEHAVIOUR_MESSAGE"].includes(subtype)) {
      switch (subtype) {
        case "TYPING":
          setIsBotTyping(true);
          setIsBotThinking(false);
          break;
        case "THINKING":
          setIsBotThinking(true);
          setIsBotTyping(false);
          break;
        case "BEHAVIOUR_MESSAGE":
          setIsBotTyping(false);
          setIsBotThinking(false);
          break;
      }
      return;
    }

    const displayText = message || content;
    if (!displayText) return;

    const displayType = ["TEXT", "MESSAGE", "DATA_MESSAGE"].includes(subtype)
      ? subtype
      : "UNKNOWN";

    setMessages((prev) => [
      ...prev,
      {
        text: displayText,
        timestamp: Date.now(),
        type: displayType,
        source: "BOT",
      },
    ]);

    setIsBotTyping(false);
    setIsBotThinking(false);
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (message.trim()) {
      const userMessage = message.trim();

      setMessages((prev) => [
        ...prev,
        {
          text: userMessage,
          timestamp: Date.now(),
          type: "TEXT",
          source: "USER",
        },
      ]);
      setMessage("");
      setIsTyping(false);

      // Reset textarea
      if (inputRef.current) {
        inputRef.current.style.height = "104px";
        inputRef.current.scrollTop = 0;
        inputRef.current.style.overflowY = isIOS() ? "hidden" : "auto";
        inputRef.current.classList.add("reset-height");
      }

      try {
        const userId = localStorage.getItem("userID");
        if (userId) {
          await axios.post(API_ENDPOINTS.CHAT_SEND, {
            userId: userId,
            message: userMessage,
            typs: "TEXT",
            source: "USER",
            isText: query.isTest === "1" ? true : false,
          });
        }
      } catch (error) {
        setMessages((prev) => [
          ...prev,
          {
            text: "Sorry, there was an error sending your message. Please try again.",
            timestamp: Date.now(),
            type: "TEXT",
            source: "BOT",
          },
        ]);
      }
    }
  };

  const handleTextareaResize = (textarea) => {
    if (textarea) {
      textarea.style.height = "104px";
      const scrollHeight = textarea.scrollHeight;
      if (isIOS()) {
        if (scrollHeight > 48) {
          textarea.style.overflowY = "auto";
        } else {
          textarea.style.overflowY = "hidden";
        }
      } else {
        const newHeight = Math.min(250, Math.max(104, scrollHeight));
        textarea.style.height = `${newHeight}px`;
        textarea.style.overflowY = scrollHeight > newHeight ? "auto" : "hidden";
      }
    }
  };

  const handleInputChange = (e) => {
    const value = e.target.value;
    setMessage(value);
    handleTextareaResize(e.target);
    if (messages.length === 0) {
      setIsTyping(value.length > 0);
    }
  };

  useEffect(() => {
    if (messages.length > 0 && inputRef.current && !message) {
      const timer = setTimeout(() => {
        if (inputRef.current) {
          inputRef.current.style.height = "104px";
          inputRef.current.style.overflowY = isIOS() ? "hidden" : "auto";
          inputRef.current.classList.add("reset-height");
        }
      }, 50);
      return () => clearTimeout(timer);
    }
  }, [messages, message]);

  useEffect(() => {
    if (messages.length > 0) {
      scrollToBottom();
    }
  }, [messages]);

  const scrollToBottom = () => {
    if (isMobile) {
      if (mobileMessagesContainerRef.current) {
        mobileMessagesContainerRef.current.scrollTop =
          mobileMessagesContainerRef.current.scrollHeight;
      }
    } else {
      if (desktopMessagesContainerRef.current) {
        const container = desktopMessagesContainerRef.current;
        container.scrollTop = container.scrollHeight;
      }
    }
  };

  const handleKeyPress = (e) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      handleSubmit(e);
    }
  };

  const handleSuggestionClick = async (cardTitle) => {
    setMessages((prev) => [
      ...prev,
      { text: cardTitle, timestamp: Date.now(), type: "TEXT", source: "USER" },
    ]);
    setMessage("");
    setIsTyping(false);

    if (inputRef.current) {
      inputRef.current.style.height = "104px";
      inputRef.current.scrollTop = 0;
      inputRef.current.style.overflowY = isIOS() ? "hidden" : "auto";
      inputRef.current.classList.add("reset-height");
    }

    try {
      const userId = localStorage.getItem("userID");
      if (userId) {
        await axios.post(API_ENDPOINTS.CHAT_SEND, {
          userId: userId,
          message: cardTitle,
          customerName: username,
        });
      }
    } catch (error) {
      setMessages((prev) => [
        ...prev,
        {
          text: "Sorry, there was an error sending your message. Please try again.",
          timestamp: Date.now(),
          type: "TEXT",
          source: "BOT",
        },
      ]);
    }
  };

  const getMaxSlides = () => {
    if (isMobile) return SUGGESTION_CARDS.length;
    return Math.ceil(SUGGESTION_CARDS.length / 2);
  };

  const nextSlide = () => {
    if (isMobile) {
      setCurrentSlide((prev) =>
        prev < SUGGESTION_CARDS.length - 1 ? prev + 1 : prev
      );
    } else {
      const maxSlides = getMaxSlides();
      setCurrentSlide((prev) => (prev + 1) % maxSlides);
    }
  };

  const prevSlide = () => {
    if (isMobile) {
      setCurrentSlide((prev) => (prev > 0 ? prev - 1 : prev));
    } else {
      const maxSlides = getMaxSlides();
      setCurrentSlide((prev) => (prev - 1 + maxSlides) % maxSlides);
    }
  };

  const handleNextSlide = () => {
    nextSlide();
  };

  const handlePrevSlide = () => {
    prevSlide();
  };

  const handleTouchStart = (e) => {
    setTouchStart(e.targetTouches[0].clientX);
  };

  const handleTouchMove = (e) => {
    setTouchEnd(e.targetTouches[0].clientX);
  };

  const handleTouchEnd = () => {
    if (!touchStart || !touchEnd) return;
    const swipeDistance = touchStart - touchEnd;
    const minSwipeDistance = 50;

    if (swipeDistance > minSwipeDistance) {
      nextSlide();
    }
    if (swipeDistance < -minSwipeDistance) {
      prevSlide();
    }

    setTouchStart(0);
    setTouchEnd(0);
  };

  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  }, [messages]);

  useEffect(() => {
    if (messages.length === 0) {
      setShowInitialUI(false);
      const timer = setTimeout(() => setShowInitialUI(true), 60);
      return () => clearTimeout(timer);
    } else {
      setShowInitialUI(false);
    }
  }, [messages.length]);

  useEffect(() => {
    const checkMobile = () => {
      if (typeof window !== "undefined") {
        setIsMobile(window.innerWidth < 768);
      }
    };

    checkMobile();

    const handleResize = () => {
      setCurrentSlide(0);
      checkMobile();
    };

    if (typeof window !== "undefined") {
      window.addEventListener("resize", handleResize);
      return () => window.removeEventListener("resize", handleResize);
    }
  }, []);

  useEffect(() => {
    if (inputRef.current && messages.length === 0) {
      const shouldAutoFocus = window.innerWidth >= 768;
      if (shouldAutoFocus) {
        const timer = setTimeout(() => {
          inputRef.current?.focus();
        }, 100);
        return () => clearTimeout(timer);
      }
    }
  }, [messages.length]);

  useEffect(() => {
    return () => {
      if (sseConnection) {
        sseConnection.close();
      }
    };
  }, [sseConnection]);

  return (
    <div className="bg-white flex flex-col">
      {/* Desktop Layout (≥1024px) */}
      <div className="hidden lg:flex flex-1 flex-col px-4">
        {messages.length === 0 ? (
          <div className="flex flex-1 items-center justify-center min-h-[calc(100vh-64px)]">
            <div className="flex flex-col items-center w-[768px] justify-center mx-auto">
              <h1
                className={`text-4xl text-gray-900 mb-6 text-center transition-all duration-700 ${
                  showInitialUI
                    ? "opacity-100 translate-y-0"
                    : "opacity-0 translate-y-4"
                }`}
                style={{ transitionDelay: showInitialUI ? "40ms" : "0ms" }}
              >
                How can I help you?
              </h1>
              <form
                onSubmit={handleSubmit}
                className={`relative w-full mb-6 flex flex-col items-center transition-all duration-500 ${
                  showInitialUI
                    ? "opacity-100 translate-y-0"
                    : "opacity-0 translate-y-4"
                }`}
                style={{ transitionDelay: showInitialUI ? "120ms" : "0ms" }}
              >
                <textarea
                  ref={messages.length === 0 ? inputRef : null}
                  value={message}
                  onChange={handleInputChange}
                  onKeyDown={handleKeyPress}
                  placeholder="Ask anything"
                  autoFocus={messages.length === 0}
                  rows={1}
                  className="pt-3 px-4 lg:p-4 pb-16 pr-12 w-full min-h-[104px] max-h-[250px] overflow-y-auto hide-scrollbar text-black rounded-[20px] outline-none border border-[#E0E2D9] text-base shadow-md resize-none reset-height"
                  style={{
                    boxSizing: "border-box",
                    zIndex: 1001,
                    position: "relative",
                  }}
                />
                <button
                  type="submit"
                  disabled={message.trim().length === 0}
                  className={`absolute right-3 bottom-3 w-9 h-9 rounded-full flex items-center justify-center transition-colors duration-200 z-1002 touch-manipulation ${
                    message.trim().length === 0
                      ? "bg-[#D4D6CE] text-white cursor-not-allowed"
                      : "bg-black text-white hover:bg-gray-800"
                  }`}
                >
                  <FaArrowUp className="w-4 h-4 text-white" />
                </button>
              </form>
              <div
                className={`relative w-full max-w-2xl transition-all duration-500 ease-in-out ${
                  isTyping
                    ? "opacity-0 pointer-events-none"
                    : showInitialUI
                    ? "opacity-100 translate-y-0"
                    : "opacity-0 translate-y-4"
                }`}
                style={{
                  transitionDelay: showInitialUI ? "200ms" : "0ms",
                  zIndex: 10,
                }}
              >
                <div className="overflow-hidden">
                  <div
                    ref={carouselRef}
                    className="flex transition-transform duration-300 ease-in-out"
                    style={{ transform: `translateX(-${currentSlide * 100}%)` }}
                  >
                    {Array.from({
                      length: Math.ceil(SUGGESTION_CARDS.length / 2),
                    }).map((_, slideIndex) => (
                      <div
                        key={slideIndex}
                        className="w-full flex-shrink-0 flex gap-2 justify-center"
                      >
                        {SUGGESTION_CARDS.slice(
                          slideIndex * 2,
                          slideIndex * 2 + 2
                        ).map((card, cardIndex) => (
                          <button
                            key={slideIndex * 2 + cardIndex}
                            onClick={() => handleSuggestionClick(card.title)}
                            className="py-2 px-3 rounded-[8px] bg-[#f6f6f6] text-left group transition-all duration-200 touch-manipulation"
                            style={{ width: "fit-content" }}
                          >
                            <div className="text-[16px] font-[600] text-black mb-0.5 leading-tight whitespace-nowrap">
                              {card.title}
                            </div>
                            <div className="text-[16px] text-gray-500 leading-tight whitespace-nowrap">
                              {card.subtitle}
                            </div>
                          </button>
                        ))}
                      </div>
                    ))}
                  </div>
                </div>
                <button
                  onClick={handlePrevSlide}
                  className="absolute left-28 top-1/2 transform -translate-y-1/2 -translate-x-6 w-8 h-8 bg-white border border-gray-200 rounded-full flex items-center justify-center hover:bg-gray-50 shadow-md transition-all duration-200 touch-manipulation"
                >
                  <FaChevronLeft className="w-3 h-9 text-gray-600" />
                </button>
                <button
                  onClick={handleNextSlide}
                  className="absolute right-28 top-1/2 transform -translate-y-1/2 translate-x-6 w-8 h-8 bg-white border border-gray-200 rounded-full flex items-center justify-center hover:bg-gray-50 shadow-md transition-all duration-200 touch-manipulation"
                >
                  <FaChevronRight className="w-3 h-3 text-gray-600" />
                </button>
              </div>
            </div>
          </div>
        ) : (
          <>
            <div
              ref={desktopMessagesContainerRef}
              className="flex-1 overflow-y-auto max-h-[calc(100vh-340px)] pt-8 pb-20 hide-scrollbar"
              style={{ overscrollBehavior: "contain" }}
            >
              <div className="w-full lg:w-[768px] mx-auto px-4">
                <div className="space-y-4 flex flex-col">
                  {messages.map((msg, index) => (
                    <div
                      key={index}
                      className={`flex ${
                        msg.source === "USER" ? "justify-end" : "justify-start"
                      }`}
                    >
                      <div
                        className={`text-[16px] font-[400] rounded-3xl max-w-xs lg:max-w-lg break-words hyphens-auto ${
                          msg.source === "USER" ? "bg-gray-100" : "bg-white"
                        }`}
                        style={{
                          padding: "0.7rem",
                          overflowWrap: "break-word",
                          wordBreak: "break-word",
                        }}
                      >
                        {msg.text}
                      </div>
                    </div>
                  ))}

                  {/* Bot loading indicator */}
                  {(isBotTyping || isBotThinking) && (
                    <div className="flex justify-start">
                      <div className="bg-white px-4 py-2">
                        <div className="flex space-x-1">
                          <div className="w-2 h-2 bg-black rounded-full animate-[scale_0.8s_ease-in-out_infinite]"></div>
                          <div className="w-2 h-2 bg-black rounded-full animate-[scale_0.8s_ease-in-out_infinite]"></div>
                          <div className="w-2 h-2 bg-black rounded-full animate-[scale_0.8s_ease-in-out_infinite]"></div>
                        </div>
                      </div>
                    </div>
                  )}

                  <div ref={messagesEndRef} />
                </div>
              </div>
            </div>

            <div
              className="fixed bottom-0 left-0 right-0 p-4 mt-5"
              style={{ zIndex: 1000, marginTop: "20px" }}
            >
              <form
                onSubmit={handleSubmit}
                className="relative w-full lg:w-[768px] mx-auto"
              >
                <textarea
                  ref={messages.length > 0 ? inputRef : null}
                  value={message}
                  onChange={handleInputChange}
                  onKeyDown={handleKeyPress}
                  placeholder="Ask anything"
                  rows={1}
                  className="pt-3 px-4 md:px-5 pb-10 pr-12 w-full min-h-[104px] max-h-[250px] overflow-y-auto text-black rounded-[20px] outline-none border border-[#E0E2D9] text-base shadow-lg resize-none reset-height hide-scrollbar"
                  style={{
                    boxSizing: "border-box",
                    zIndex: 1001,
                    position: "relative",
                  }}
                />
                <button
                  type="submit"
                  disabled={message.trim().length === 0}
                  className={`absolute right-3 bottom-3 w-8 h-8 rounded-full flex items-center justify-center transition-colors duration-200 z-1002 touch-manipulation ${
                    message.trim().length === 0
                      ? "bg-[#D4D6CE] text-white cursor-not-allowed"
                      : "bg-black text-white hover:bg-gray-800"
                  }`}
                >
                  <FaArrowUp className="w-4 h-4 text-white" />
                </button>
              </form>
              <div className="text-center mt-2">
                <p className="text-xs text-gray-500">
                  This chat is powered by{" "}
                  <strong className="text-black">Driply.me</strong>
                </p>
              </div>
            </div>
          </>
        )}
      </div>

      {/* Mobile/Tablet Layout (<1024px) */}
      <div className="lg:hidden overflow-hidden fixed inset-0 flex flex-col mt-5">
        {messages.length === 0 ? (
          <>
            <div className="flex flex-col items-center pt-15 pb-4 px-4">
              <div className="hidden md:flex lg:hidden flex-col items-center"></div>
            </div>
            <div
              className="flex flex-col items-center justify-center flex-1 px-4"
              style={{ paddingBottom: "260px" }}
            >
              <h1
                className={`text-2xl md:text-4xl text-gray-900 text-center leading-relaxed transition-all duration-700 ${
                  showInitialUI
                    ? "opacity-100 translate-y-0"
                    : "opacity-0 translate-y-4"
                }`}
                style={{ transitionDelay: showInitialUI ? "40ms" : "0ms" }}
              >
                How can I help you?
              </h1>
            </div>
          </>
        ) : (
          <>
            <div
              ref={mobileMessagesContainerRef}
              className="flex-1 overflow-y-auto bg-white hide-scrollbar pt-[80px] pb-[160px]"
            >
              <div className="w-full max-w-[803px] mx-auto px-4 pb-6 space-y-4">
                {messages.map((msg, index) => (
                  <div
                    key={index}
                    className={`flex w-full ${
                      msg.source === "USER" ? "justify-end" : "justify-start"
                    }`}
                    ref={messagesEndRef}
                  >
                    <div
                      className={`px-3 py-2 font-[400] rounded-[15px] text-[16px] max-w-[80%] sm:max-w-[70%] md:max-w-[65%] break-words hyphens-auto ${
                        msg.source === "USER" ? "bg-gray-100" : "bg-white"
                      }`}
                      style={{
                        overflowWrap: "break-word",
                        overflow: "hidden",
                        wordBreak: "break-all",
                        maxWidth: "90%",
                      }}
                    >
                      {msg.text}
                    </div>
                  </div>
                ))}

                {/* Bot loading indicator for mobile */}
                {(isBotTyping || isBotThinking) && (
                  <div className="flex justify-start w-full">
                    <div className="bg-white px-3 py-2">
                      <div className="flex space-x-1">
                        <div className="w-2 h-2 bg-black rounded-full animate-[scale_0.8s_ease-in-out_infinite]"></div>
                        <div className="w-2 h-2 bg-black rounded-full animate-[scale_0.8s_ease-in-out_infinite]"></div>
                        <div className="w-2 h-2 bg-black rounded-full animate-[scale_0.8s_ease-in-out_infinite]"></div>
                      </div>
                    </div>
                  </div>
                )}

                <div ref={messagesEndRef} />
              </div>
            </div>

            <div
              className="fixed bottom-0 left-0 right-0 bg-white"
              style={{
                minHeight: "160px",
                zIndex: 1000,
                paddingBottom: "env(safe-area-inset-bottom, 0)",
                transform: "translateZ(0)",
                backfaceVisibility: "hidden",
              }}
            >
              <div
                className="px-4 py-4 bg-white"
                style={{ transform: "translateZ(0)" }}
              >
                <form
                  onSubmit={handleSubmit}
                  className="relative bg-white"
                  style={{ zIndex: 1001 }}
                >
                  <div
                    className="relative w-full max-w-[890px] mx-auto bg-white"
                    style={{ transform: "translateZ(0)" }}
                  >
                    <textarea
                      ref={messages.length > 0 ? inputRef : null}
                      value={message}
                      onChange={handleInputChange}
                      onKeyDown={handleKeyPress}
                      placeholder="Ask anything"
                      rows={1}
                      className="w-full min-h-[104px] pt-3 px-4 pb-10 pr-12 text-black rounded-[20px] outline-none border border-[#E0E2D9] text-base resize-none overflow-y-auto reset-height"
                      style={{
                        boxSizing: "border-box",
                        maxHeight: "250px",
                        zIndex: 1001,
                        overscrollBehavior: "none",
                        position: "relative", // Ensure textarea stays in place
                      }}
                    />
                    <button
                      type="submit"
                      disabled={message.trim().length === 0}
                      className={`absolute right-3 bottom-3 w-8 h-8 rounded-full flex items-center justify-center transition-colors duration-200 z-1002 touch-manipulation ${
                        message.trim().length === 0
                          ? "bg-[#E0E0E0] text-white cursor-not-allowed"
                          : "bg-black text-white hover:bg-gray-800"
                      }`}
                    >
                      <FaArrowUp className="w-4 h-4 text-white" />
                    </button>
                  </div>
                </form>
                <div className="text-center py-2">
                  <p className="text-xs text-gray-500">
                    This chat is powered by{" "}
                    <strong className="text-black">Driply.me</strong>
                  </p>
                </div>
              </div>
            </div>
          </>
        )}

        {/* Mobile/Tablet Bottom Section */}
        <div className="absolute bottom-0 left-0 right-0 bg-white">
          {messages.length === 0 && (
            <>
              {/* Suggestion Cards Section */}
              <div
                className={`${
                  isMobile ? "px-0" : "px-4"
                } pt-2 pb-2 transition-all duration-500 ease-in-out ${
                  isTyping
                    ? "opacity-0 pointer-events-none"
                    : showInitialUI
                    ? "opacity-100 translate-y-0"
                    : "opacity-0 translate-y-4"
                }`}
                style={{
                  transitionDelay: showInitialUI ? "120ms" : "0ms",
                  position: "relative",
                  zIndex: 10,
                  backgroundColor: "white",
                  paddingBottom: "10px",
                }}
              >
                <div className="overflow-hidden">
                  <div
                    ref={carouselRef}
                    className="flex transition-transform duration-300 ease-in-out"
                    style={{
                      transform: `translateX(-${
                        currentSlide * (isMobile ? 50 : 100)
                      }%)`,
                      paddingLeft: isMobile ? "1rem" : "0",
                    }}
                    onTouchStart={handleTouchStart}
                    onTouchMove={handleTouchMove}
                    onTouchEnd={handleTouchEnd}
                  >
                    {isMobile
                      ? (() => {
                          const circularCards = [
                            ...SUGGESTION_CARDS,
                            ...SUGGESTION_CARDS,
                          ];
                          return circularCards.map((card, index) => (
                            <div
                              key={`${index}-${card.title}`}
                              className="flex-shrink-0 mt-3 mr-2"
                            >
                              <button
                                onClick={() =>
                                  handleSuggestionClick(card.title)
                                }
                                className="py-3 px-2.5 rounded-[8px] bg-[#F6F6F6] text-left hover:bg-[#D6EDFF] transition-all duration-200 inline-block touch-manipulation"
                                style={{ width: "fit-content" }}
                              >
                                <div className="text-[16px] font-[600] text-black mb-0.5 leading-tight whitespace-nowrap overflow-hidden text-ellipsis">
                                  {card.title}
                                </div>
                                <div className="text-[16px] text-gray-500 leading-tight whitespace-nowrap overflow-hidden text-ellipsis">
                                  {card.subtitle}
                                </div>
                              </button>
                            </div>
                          ));
                        })()
                      : Array.from({
                          length: Math.ceil(SUGGESTION_CARDS.length / 2),
                        }).map((_, slideIndex) => (
                          <div
                            key={slideIndex}
                            className="w-full flex-shrink-0 flex gap-2 justify-center"
                          >
                            {SUGGESTION_CARDS.slice(
                              slideIndex * 2,
                              slideIndex * 2 + 2
                            ).map((card, cardIndex) => (
                              <button
                                key={slideIndex * 2 + cardIndex}
                                onClick={() =>
                                  handleSuggestionClick(card.title)
                                }
                                className="py-2 px-2.5 rounded-[8px] bg-[#F6F6F6] text-left group hover:bg-[#D6EDFF] transition-all duration-200 inline-block touch-manipulation"
                                style={{ width: "fit-content" }}
                              >
                                <div className="text-[14px] font-[600] text-black mb-0.5 leading-tight whitespace-nowrap">
                                  {card.title}
                                </div>
                                <div className="text-[16px] text-gray-500 leading-tight whitespace-nowrap">
                                  {card.subtitle}
                                </div>
                              </button>
                            ))}
                          </div>
                        ))}
                  </div>
                </div>

                {/* Navigation Buttons */}
                <button
                  onClick={handlePrevSlide}
                  className="hidden md:block absolute left-28 top-1/2 transform -translate-y-1/2 -translate-x-6 w-8 h-8 bg-white border border-gray-200 rounded-full items-center justify-center hover:bg-gray-50 shadow-md transition-all duration-200 touch-manipulation"
                >
                  <FaChevronLeft className="w-3 h-3 text-gray-600" />
                </button>
                <button
                  onClick={handleNextSlide}
                  className="hidden md:block absolute right-28 top-1/2 transform -translate-y-1/2 translate-x-6 w-8 h-8 bg-white border border-gray-200 rounded-full items-center justify-center hover:bg-gray-50 shadow-md transition-all duration-200 touch-manipulation"
                >
                  <FaChevronRight className="w-3 h-3 text-gray-600" />
                </button>
              </div>

              {/* Input Section */}
              <div
                className={`px-4 bg-white transition-all duration-500 ${
                  showInitialUI ? "opacity-100 translate-y-0" : "translate-y-4"
                }`}
                style={{
                  transitionDelay: showInitialUI ? "200ms" : "0ms",
                  position: "relative",
                  zIndex: 5,
                  paddingBottom: "env(safe-area-inset-bottom)",
                }}
              >
                <form onSubmit={handleSubmit} className="relative">
                  <div className="relative w-full max-w-[890px] mx-auto">
                    <textarea
                      ref={messages.length === 0 ? inputRef : null}
                      value={message}
                      onChange={handleInputChange}
                      onKeyDown={handleKeyPress}
                      placeholder="Ask anything"
                      rows={1}
                      className="w-full min-h-[104px] pt-3 px-4 pb-10 pr-12 text-black rounded-[20px] outline-none border border-[#E0E2D9] text-base resize-none overflow-y-auto reset-height"
                      style={{
                        boxSizing: "border-box",
                        maxHeight: "250px",
                        fontSize: "16px",
                        zIndex: 1001,
                        overscrollBehaviorY: "contain", // Prevent vertical overscroll affecting layout
                        position: "relative", // Ensure textarea stays in its container
                      }}
                    />
                    <button
                      type="submit"
                      disabled={message.trim().length === 0}
                      className={`absolute right-3 bottom-3 w-8 h-8 rounded-full flex items-center justify-center transition-colors duration-200 z-1002 touch-manipulation ${
                        message.trim().length === 0
                          ? "bg-[#D4D6CE] text-white cursor-not-allowed"
                          : "bg-black text-white hover:bg-gray-800"
                      }`}
                    >
                      <FaArrowUp className="w-4 h-4 text-white" />
                    </button>
                  </div>
                </form>
                <div className="text-center py-2">
                  <p className="text-xs text-gray-500">
                    This chat is powered by{" "}
                    <strong className="text-black">Driply.me</strong>
                  </p>
                </div>
              </div>
            </>
          )}
        </div>
      </div>

      {/* CSS to enforce max-height and prevent overlap */}
      <style jsx>{`
        @keyframes scale {
          0%,
          100% {
            transform: scale(0.5);
            opacity: 0.5;
          }
          50% {
            transform: scale(1);
            opacity: 1;
          }
        }

        /* Global scrollbar hiding styles */
        :global(.hide-scrollbar) {
          -ms-overflow-style: none !important; /* IE and Edge */
          scrollbar-width: none !important; /* Firefox */
        }
        :global(.hide-scrollbar::-webkit-scrollbar) {
          display: none !important; /* Chrome, Safari and Opera */
        }

        @media only screen and (max-width: 1023px) {
          /* Fix for mobile keyboard pushing textarea */
          .lg\\:hidden .absolute.bottom-0 {
            position: fixed !important;
            bottom: env(safe-area-inset-bottom, 0);
            left: 0;
            right: 0;
            width: 100%;
            box-sizing: border-box;
            z-index: 1000;
            background-color: white;
          }

          /* Enhanced textarea styles for all mobile devices */
          textarea {
            -webkit-user-select: auto !important;
            user-select: auto !important;
            -webkit-appearance: none;
            appearance: none;
            overscroll-behavior: none;
            line-height: 24px;
            max-height: 250px !important;
            position: relative !important;
            transform: translateZ(0); /* Force hardware acceleration */
            backface-visibility: hidden;
            perspective: 1000;
            z-index: 1001 !important;
            background-color: white;
          }

          /* Form container fixes */
          .lg\\:hidden form {
            position: relative !important;
            z-index: 1001;
            background-color: white;
          }

          /* Ensure submit button stays visible */
          button[type="submit"] {
            position: absolute !important;
            right: 12px;
            bottom: 12px;
            z-index: 1002 !important;
            -webkit-tap-highlight-color: transparent;
            touch-action: manipulation;
            transform: translateZ(0);
          }

          /* Prevent any background content from showing through */
          .lg\\:hidden .bg-white {
            background-color: white !important;
          }
        }

        /* Desktop-specific styles */
        @media only screen and (min-width: 1024px) {
          textarea {
            max-height: 250px !important;
            overflow-y: auto !important;
            overscroll-behavior: contain;
          }
          .reset-height {
            min-height: 104px !important;
            max-height: 250px !important;
            overflow-y: auto !important;
          }
        }

        /* Common styles for better mobile handling */
        .fixed-bottom {
          position: fixed !important;
          bottom: 0;
          left: 0;
          right: 0;
          background: white;
        }
      `}</style>
    </div>
  );
};

export default ChatInterface;
